<template>
  <q-page class="flex flex-center bg-white relative q-pa-md">
    <div class="absolute-top-right q-pa-md" style="z-index: 9999">
      <router-link to="/auth/login" flat no-caps color="primary" label="Skip" class="text-weight-bold">
        Skip
      </router-link>
    </div>
    <q-carousel v-model="slide" animated height="100vh" class="full-width bg-white" swipeable :infinite="false">
      <!-- Slide 1 -->
      <q-carousel-slide :name="1" class="column items-center justify-between q-pa-md bg-white">
        <q-img src="/images/onboarding1.svg" alt="Sport Booking App" style="flex: 1" fit="contain" srcset="
            /images/onboarding1.svg 300w,
            /images/onboarding1.svg 2x,
            /images/onboarding1.svg 3x,
            /images/onboarding1.svg 4x
          " width="100%" max-width="300px" />
        <div class="column items-center justify-center" style="flex: 1">
          <div class="text-h4 text-weight-bold text-dark q-mb-none text-center full-width" style="white-space: nowrap">
            Unlock the Future of
          </div>
          <span class="text-primary text-h4 text-weight-bold q-mb-md">Sport Booking App</span>
          <p class="text-body2 text-center q-mb-md" style="color: #b0b0b0">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
            incididunt
          </p>
          <q-btn unelevated rounded color="primary" text-color="white" size="lg" class="full-width"
            style="font-weight: bold; font-size: 18px; border-radius: 32px; height: 52px" label="Let's Get Started"
            @click="slide = 2" />
          <div class="text-body2 q-mt-md" style="color: #b0b0b0">
            Already have an account?
            <router-link to="/auth/login" class="text-primary q-pa-none" style="font-weight: 500; text-decoration: none"
              @click="markIntroAsSeen">
              Sign In
            </router-link>
          </div>
        </div>
      </q-carousel-slide>

      <!-- Slide 2 -->
      <q-carousel-slide :name="2" class="column items-center justify-between q-pa-lg bg-white">
        <q-img src="/images/onboarding2.svg" alt="Interactive Map" style="flex: 1" fit="contain" srcset="
           /images/onboarding2.svg 300w,
            /images/onboarding2.svg 2x,
            /images/onboarding2.svg 3x,
            /images/onboarding2.svg 4x
          " width="100%" max-width="300px" />
        <div class="column items-center justify-center" style="flex: 1">
          <h4 class="text-weight-bold q-mb-md text-center text-primary full-width">
            Navigate Events Using <span class="text-dark">an Interactive Map</span>
          </h4>
          <div class="text-body2 text-center q-mb-md" style="color: #b0b0b0">
            Lorem ipsum dolor sit amet, consectetur<br />
            adipiscing elit, sed do eiusmod tempor<br />
            incididunt
          </div>
          <div v-if="slide >= 2" class="row items-center justify-between full-width q-mt-md"
            style="bottom: 48px; left: 0; right: 0; max-width: 430px; margin: 0 auto">
            <q-btn round outline color="primary" icon="arrow_back" :disable="slide === 1" @click="slide--" />
            <div class="row items-center justify-center" style="gap: 18px; min-width: 70px">
              <div v-for="n in 3" :key="n" :style="{
                width: '14px',
                height: '14px',
                borderRadius: '50%',
                background: slide === n ? '#FF9900' : '#FFD6B3',
                opacity: slide === n ? 1 : 0.7,
                transition: 'background 0.2s',
              }" />
            </div>
            <q-btn round color="primary" text-color="white" icon="arrow_forward" @click="slide++" />
          </div>
        </div>
      </q-carousel-slide>
      <!-- Slide 2 -->
      <q-carousel-slide :name="3" class="column items-center justify-between q-pa-lg bg-white">
        <q-img src="/images/onboarding3.svg" alt="Interactive Map" style="flex: 1" fit="contain" srcset="
            /images/onboarding3.svg 300w,
            /images/onboarding3.svg 2x,
            /images/onboarding3.svg 3x,
            /images/onboarding3.svg 4x
          " width="100%" max-width="300px" />
        <div class="column items-center justify-center" style="flex: 1">
          <h4 class="text-weight-bold q-mb-md text-center text-primary full-width">
            Book Sports Venues <span class="text-dark">With Just a Few Taps</span>
          </h4>
          <div class="text-body2 text-center q-mb-md" style="color: #b0b0b0">
            Find and reserve your favorite sports facilities<br />
            instantly with our easy-to-use booking system<br />
            designed for sports enthusiasts
          </div>
          <div v-if="slide >= 2" class="row items-center justify-between full-width q-mt-lg"
            style="bottom: 48px; left: 0; right: 0; max-width: 430px; margin: 0 auto">
            <q-btn round outline color="primary" icon="arrow_back" :disable="slide === 1" @click="slide--" />
            <div class="row items-center justify-center" style="gap: 18px; min-width: 70px">
              <div v-for="n in 3" :key="n" :style="{
                width: '14px',
                height: '14px',
                borderRadius: '50%',
                background: slide === n ? '#FF9900' : '#FFD6B3',
                opacity: slide === n ? 1 : 0.7,
                transition: 'background 0.2s',
              }" />
            </div>
            <q-btn round color="primary" text-color="white" icon="arrow_forward" @click="handleComplete" />
          </div>
        </div>
      </q-carousel-slide>
    </q-carousel>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const slide = ref(1);

const markIntroAsSeen = () => {
  localStorage.setItem('introSeen', 'true');
};

const handleComplete = () => {
  markIntroAsSeen();
  router.push('/auth/login');
};
</script>
