import { defineStore } from 'pinia';
import { userService } from 'src/services/userService';
import type { IUser } from 'src/types/auth';
import { ref } from 'vue';

export const useUserStore = defineStore('user', () => {
  const user = ref<IUser | null>(null);

  const updateUserProfile = async (data: {
    displayName: string;
    phoneNumber: string;
    phoneVerified?: boolean;
    metadata?: any;
  }) => {
    try {
      const response = await userService.updateProfile(data);
      user.value = response.data.user;
    } catch (err) {
      console.error(err, 'Failed to update profile');
    }
  };

  const getUserProfile = async () => {
    try {
      const response = await userService.getProfile();
      user.value = response.data.user;
      return response.data.user;
    } catch (err) {
      console.error(err, 'Failed to get profile');
    }
  };

  return {
    user,
    updateUserProfile,
    getUserProfile,
  };
});
