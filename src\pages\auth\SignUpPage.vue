<template>
  <q-page class="column items-center justify-center q-pa-md">
    <q-card-section class="text-center q-pb-none">
      <div class="text-h4 text-weight-bold text-dark q-mb-sm">Create Account</div>
      <p class="text-body2 text-grey-6">Join our sport booking community</p>
    </q-card-section>
    <q-card-section class="full-width">
      <q-form @submit="handleEmailSignup" class="q-gutter-y-md">
        <q-input v-model="displayName" label="Display Name" outlined rounded
          :rules="[(val) => !!val || 'Display name is required']" />

        <q-input v-model="email" type="email" label="Email" outlined rounded :rules="[
          (val) => !!val || 'Email is required',
          (val) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Invalid email format',
        ]" />

        <q-input v-model="password" outlined rounded :type="showPassword ? 'text' : 'password'" label="Password" :rules="[
          (val) => !!val || 'Password is required',
          (val) => val.length >= 6 || 'Password must be at least 6 characters',
        ]">
          <template v-slot:append>
            <q-icon :name="showPassword ? 'visibility_off' : 'visibility'" class="cursor-pointer"
              @click="showPassword = !showPassword" />
          </template>
        </q-input>

        <div class="row items-center q-mb-md">
          <q-checkbox v-model="agreeToTerms"
            :rules="[(val: boolean) => val || 'You must agree to the Terms and Conditions']">
            <template v-slot:default>
              I agree to the
              <router-link to="#"> Terms and Conditions </router-link>
            </template>
          </q-checkbox>
        </div>

        <q-btn type="submit" :disabled="loading" color="primary" class="full-width" size="lg"> Create Account </q-btn>

        <div class="social-login-container">
          <!-- Divider -->
          <q-separator class="q-my-lg" />
          <div class="text-center text-grey-6 q-mb-md">Or</div>

          <!-- Social Login Buttons -->
          <div class="row justify-center q-gutter-md">
            <q-btn :disabled="loading" round color="grey-2" size="lg" @click="handleGoogleSignup" class="social-btn">
              <GoogleIcon />
            </q-btn>
          </div>
        </div>

        <p class="text-center q-ma-none q-mt-md">
          Already have an account?
          <router-link to="/auth/login"> Sign In </router-link>
        </p>
      </q-form>
    </q-card-section>

    <q-dialog v-model="showError" persistent>
      <q-card>
        <q-card-section>
          <div class="text-h6">Error</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          {{ authStore.error }}
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="OK" color="primary" v-close-popup @click="authStore.error = null" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import GoogleIcon from 'src/components/Icons/GoogleIcon.vue';
import { useAuthStore } from 'src/stores/authStore';
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

const $q = useQuasar();
const router = useRouter();
const authStore = useAuthStore();

const email = ref('');
const password = ref('');
const displayName = ref('');
const showPassword = ref(false);
const agreeToTerms = ref(false);
const loading = ref(false);

const showError = computed(() => !!authStore.error);

async function handleEmailSignup() {
  if (!email.value || !password.value || !displayName.value) return;

  loading.value = true;
  try {
    await authStore.registerWithEmail(email.value, password.value, displayName.value);
    router.replace('/auth/login');
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: error.message || 'Failed to sign up',
    });
  } finally {
    loading.value = false;
  }
}

async function handleGoogleSignup() {
  loading.value = true;
  try {
    await authStore.registerWithGoogle();
    router.replace('/auth/login');
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: error.message || 'Failed to sign up with Google',
    });
  } finally {
    loading.value = false;
  }
}
</script>
