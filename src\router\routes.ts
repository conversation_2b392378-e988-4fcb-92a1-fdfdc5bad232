import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  // Main app
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    redirect: '/home',
    children: [
      {
        path: '',
        name: 'home',
        component: () => import('pages/HomePage.vue'),
      },
    ],
  },

  // Auth routes
  {
    path: '/auth',
    component: () => import('layouts/AuthLayout.vue'),
    redirect: '/auth/intro',
    children: [
      {
        path: 'intro',
        name: 'intro',
        component: () => import('pages/auth/OnboardingPage.vue'),
        meta: { guestOnly: true },
      },
      {
        path: 'login',
        name: 'login',
        component: () => import('pages/auth/LoginPage.vue'),
        meta: { guestOnly: true },
      },
      {
        path: 'signup',
        name: 'signup',
        component: () => import('pages/auth/SignUpPage.vue'),
        meta: { guestOnly: true },
      },
    ],
  },

  // Onboarding flow
  {
    path: '/onboarding',
    component: () => import('layouts/OnboardingLayout.vue'),
    // meta: { requiresAuth: true },
    redirect: '/onboarding/profile',
    children: [
      {
        path: 'profile',
        name: 'update-profile',
        component: () => import('pages/onboarding/UpdateProfilePage.vue'),
      },
      {
        path: 'location',
        name: 'location-permission',
        component: () => import('pages/onboarding/LocationPage.vue'),
      },
      {
        path: 'sports',
        name: 'choose-sports',
        component: () => import('pages/onboarding/SportsPage.vue'),
      },
    ],
  },

  // Error page - wrap trong AuthLayout
  {
    path: '/:catchAll(.*)*',
    component: () => import('layouts/AuthLayout.vue'),
    children: [
      {
        path: '',
        name: 'error-404',
        component: () => import('pages/ErrorNotFound.vue'),
      },
    ],
  },
];
export default routes;
