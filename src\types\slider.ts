export interface SliderOptions {
  // Basic options
  slidesPerView?: number | 'auto';
  spaceBetween?: number;
  centeredSlides?: boolean;
  loop?: boolean;
  autoplay?: boolean | {
    delay?: number;
    disableOnInteraction?: boolean;
    pauseOnMouseEnter?: boolean;
  };
  
  // Navigation
  navigation?: boolean | {
    nextEl?: string;
    prevEl?: string;
  };
  
  // Pagination
  pagination?: boolean | {
    el?: string;
    clickable?: boolean;
    type?: 'bullets' | 'fraction' | 'progressbar';
    dynamicBullets?: boolean;
  };
  
  // Scrollbar
  scrollbar?: boolean | {
    el?: string;
    draggable?: boolean;
  };
  
  // Effects
  effect?: 'slide' | 'fade' | 'cube' | 'coverflow' | 'flip' | 'cards' | 'creative';
  
  // Responsive breakpoints
  breakpoints?: {
    [key: number]: {
      slidesPerView?: number | 'auto';
      spaceBetween?: number;
    };
  };
  
  // Direction
  direction?: 'horizontal' | 'vertical';
  
  // Speed
  speed?: number;
  
  // Free mode
  freeMode?: boolean;
  
  // Grab cursor
  grabCursor?: boolean;
  
  // Mousewheel
  mousewheel?: boolean | {
    forceToAxis?: boolean;
    sensitivity?: number;
  };
  
  // Keyboard
  keyboard?: boolean | {
    enabled?: boolean;
    onlyInViewport?: boolean;
  };
}

export interface SliderSlide {
  id?: string | number;
  content?: string;
  image?: string;
  title?: string;
  description?: string;
  [key: string]: any;
}

export interface SliderEvents {
  slideChange?: (swiper: any) => void;
  slideChangeTransitionStart?: (swiper: any) => void;
  slideChangeTransitionEnd?: (swiper: any) => void;
  slideNextTransitionStart?: (swiper: any) => void;
  slideNextTransitionEnd?: (swiper: any) => void;
  slidePrevTransitionStart?: (swiper: any) => void;
  slidePrevTransitionEnd?: (swiper: any) => void;
  transitionStart?: (swiper: any) => void;
  transitionEnd?: (swiper: any) => void;
  touchStart?: (swiper: any, event: TouchEvent) => void;
  touchMove?: (swiper: any, event: TouchEvent) => void;
  touchEnd?: (swiper: any, event: TouchEvent) => void;
  click?: (swiper: any, event: MouseEvent) => void;
  tap?: (swiper: any, event: TouchEvent) => void;
  reachBeginning?: (swiper: any) => void;
  reachEnd?: (swiper: any) => void;
  fromEdge?: (swiper: any) => void;
  setTranslate?: (swiper: any, translate: number) => void;
  setTransition?: (swiper: any, transition: number) => void;
  resize?: (swiper: any) => void;
  observerUpdate?: (swiper: any) => void;
  beforeLoopFix?: (swiper: any) => void;
  loopFix?: (swiper: any) => void;
  breakpoint?: (swiper: any, breakpointParams: any) => void;
}
