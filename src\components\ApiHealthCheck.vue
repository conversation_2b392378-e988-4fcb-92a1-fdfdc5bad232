<template>
  <q-card class="api-health-card">
    <q-card-section>
      <div class="text-h6">API Health Status</div>
    </q-card-section>

    <q-card-section>
      <div class="row items-center q-gutter-md">
        <q-icon :name="statusIcon" :color="statusColor" size="md" />
        <div>
          <div class="text-subtitle1">Status: {{ healthData?.status || 'Unknown' }}</div>
          <div class="text-caption text-grey-6">Base URL: {{ baseUrl }}</div>
          <div v-if="healthData?.timestamp" class="text-caption text-grey-6">
            Last checked: {{ formatTimestamp(healthData.timestamp) }}
          </div>
          <div v-if="healthData?.version" class="text-caption text-grey-6">
            Version: {{ healthData.version }}
          </div>
        </div>
      </div>
    </q-card-section>

    <q-card-actions>
      <q-btn flat color="primary" :loading="loading" @click="checkHealth"> Check Health </q-btn>
      <q-btn flat color="secondary" :loading="loading" @click="getDetailedStatus">
        Detailed Status
      </q-btn>
    </q-card-actions>

    <q-card-section v-if="error">
      <q-banner class="text-white bg-red">
        <template #avatar>
          <q-icon name="error" color="white" />
        </template>
        {{ error }}
      </q-banner>
    </q-card-section>

    <q-card-section v-if="detailedStatus">
      <q-expansion-item icon="info" label="Detailed Status Information" header-class="text-primary">
        <q-card>
          <q-card-section>
            <pre class="text-caption">{{ JSON.stringify(detailedStatus, null, 2) }}</pre>
          </q-card-section>
        </q-card>
      </q-expansion-item>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { apiService } from 'src/services/api';
import { healthService } from 'src/services/health';
import type { HealthCheckResponse } from 'src/types/api';
import { computed, onMounted, ref } from 'vue';

// Reactive state
const loading = ref(false);
const error = ref<string | null>(null);
const healthData = ref<HealthCheckResponse | null>(null);
const baseUrl = ref<string>('');
const detailedStatus = ref<any>(null);

// Computed properties
const statusIcon = computed(() => {
  if (loading.value) return 'hourglass_empty';
  if (error.value) return 'error';
  return healthData.value?.status === 'ok' ? 'check_circle' : 'cancel';
});

const statusColor = computed(() => {
  if (loading.value) return 'orange';
  if (error.value) return 'red';
  return healthData.value?.status === 'ok' ? 'green' : 'red';
});

// Methods
const checkHealth = async () => {
  loading.value = true;
  error.value = null;

  try {
    const health = await healthService.checkHealth();
    healthData.value = health;
    baseUrl.value = apiService.getBaseURL() || 'Unknown';
    console.log('baseUrl', baseUrl.value);
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to check health';
    console.error('Health check error:', err);
  } finally {
    loading.value = false;
  }
};

const getDetailedStatus = async () => {
  loading.value = true;
  error.value = null;

  try {
    const status = await healthService.getApiStatus();
    detailedStatus.value = status;
    healthData.value = status.health;
    baseUrl.value = status.baseUrl;
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to get detailed status';
    console.error('Detailed status error:', err);
  } finally {
    loading.value = false;
  }
};

const formatTimestamp = (timestamp: string): string => {
  try {
    return new Date(timestamp).toLocaleString();
  } catch {
    return timestamp;
  }
};

// Initialize on mount
onMounted(() => {
  checkHealth();
});
</script>

<style scoped>
.api-health-card {
  max-width: 500px;
}

pre {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
