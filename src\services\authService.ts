import { api } from 'src/boot/axios';
import type {
  ILoginRequest,
  ILoginResponse,
  IRefreshTokenRequest,
  IRefreshTokenResponse,
  IRegisterRequest,
  IRegisterResponse,
} from 'src/types/auth';

export const authService = {
  // Login with Firebase token
  login: async (data: ILoginRequest): Promise<ILoginResponse> => {
    const response = await api.post('/auth/login', data);
    return response.data;
  },

  // Register new user with Firebase token
  register: async (data: IRegisterRequest): Promise<IRegisterResponse> => {
    const response = await api.post('/auth/register', data);
    return response.data;
  },

  // Refresh token
  refreshToken: async (data: IRefreshTokenRequest): Promise<IRefreshTokenResponse> => {
    const response = await api.post('/auth/refresh', data);
    return response.data;
  },
};
