import { useAuth } from '@vueuse/firebase';
import {
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  signInWithEmailAndPassword,
  signInWithPopup,
} from 'firebase/auth';
import { auth, googleProvider } from 'src/config/firebase';
import { ref } from 'vue';

export function useFirebaseAuth() {
  const { isAuthenticated, user: firebaseUser } = useAuth(auth);
  const loading = ref(false);
  const error = ref<Error | null>(null);

  const signInWithGoogle = async () => {
    loading.value = true;
    error.value = null;
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const idToken = await result.user.getIdToken();
      return {
        idToken,
        user: result.user,
      };
    } catch (err) {
      error.value = err as Error;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Sign in with email and password
  const signInWithEmail = async (email: string, password: string) => {
    loading.value = true;
    error.value = null;
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      const idToken = await result.user.getIdToken();
      return {
        idToken,
        user: result.user,
      };
    } catch (err) {
      error.value = err as Error;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Sign up with email and password
  const signUpWithEmail = async (email: string, password: string) => {
    loading.value = true;
    error.value = null;
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      const idToken = await result.user.getIdToken();
      return {
        idToken,
        user: result.user,
      };
    } catch (err) {
      error.value = err as Error;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
    } catch (err) {
      error.value = err as Error;
      throw err;
    }
  };

  return {
    firebaseUser,
    isAuthenticated,
    loading,
    error,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signOut,
  };
}
