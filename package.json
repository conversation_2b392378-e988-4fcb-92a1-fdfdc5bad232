{"name": "tido-mobile", "version": "0.0.1", "private": true, "description": "Tido App", "author": "nullmastermind <<EMAIL>>", "type": "module", "scripts": {"build": "quasar build", "dev": "quasar dev", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "postinstall": "quasar prepare", "lint": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\"", "pwa:build": "quasar build -m pwa", "pwa:dev": "quasar dev -m pwa", "test": "echo \"No test specified\" && exit 0"}, "dependencies": {"@quasar/extras": "^1.16.4", "@types/js-cookie": "^3.0.6", "@vueuse/core": "^13.3.0", "@vueuse/firebase": "^13.3.0", "axios": "^1.2.1", "firebase": "^11.8.1", "js-cookie": "^3.0.5", "libphonenumber-js": "^1.12.8", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.3.0", "quasar": "^2.16.0", "register-service-worker": "^1.7.2", "swiper": "^10.3.1", "vue": "^3.4.18", "vue-i18n": "^11.0.0", "vue-router": "^4.0.12"}, "devDependencies": {"@eslint/js": "^9.14.0", "@intlify/unplugin-vue-i18n": "^4.0.0", "@quasar/app-vite": "^2.1.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/lodash": "^4.17.17", "@types/node": "^20.5.9", "@types/swiper": "^6.0.0", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.4.0", "autoprefixer": "^10.4.2", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.14", "prettier-plugin-sh": "^0.17.4", "prettier-plugin-sort-json": "^4.1.1", "typescript": "~5.5.3", "vite-plugin-checker": "^0.9.0", "vue-tsc": "^2.0.29", "workbox-build": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}, "productName": "Tido App"}