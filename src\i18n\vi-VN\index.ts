export default {
  failed: '<PERSON><PERSON> tác thất bại',
  success: 'Thao tác thành công',
  authentication: {
    signIn: 'Đăng nhập',
    description: 'Chào mừng bạn trở lại! Vui lòng đăng nhập để tiếp tục.',
    email: 'Email',
    password: '<PERSON><PERSON><PERSON> khẩu',
    enterName: '<PERSON>hập tên của bạn',
    enterEmail: 'Nhập email của bạn',
    enterPassword: 'Nhập mật khẩu của bạn',
    forgotPassword: 'Quên mật khẩu?',
    dontHaveAccount: 'Chưa có tài khoản?',
    or: 'Hoặc tiếp tục với',
    signInWithGoogle: 'Đăng nhập với Google',
    signInWithApple: 'Đăng nhập với Apple',
    signInWithFacebook: 'Đăng nhập với Facebook',

    signUp: 'Tạo tài khoản',
    signUpDescription: 'Tham gia cộng đồng để bắt đầu.',
    alreadyHaveAccount: 'Đã có tài khoản?',
    agreeToTerms: 'Bằng cách tạo tài khoản, bạn đồng ý với',
    termsOfService: 'Điều khoản & Điều kiện',
    and: 'và',
    privacyPolicy: 'Chính sách bảo mật',
  },
  profileSetup: {
    profile: {
      title: 'Hoàn thành hồ sơ của bạn',
      description: 'Đừng lo lắng, chỉ bạn mới có thể thấy dữ liệu cá nhân của mình. Không ai khác có thể nhìn thấy.',
      name: 'Tên',
      email: 'Email',
      complete: 'Hoàn thành',
    },
  },
  locationPermission: {
    title: 'Vị trí của bạn ở đâu?',
    description: 'Để tìm các sự kiện gần bạn, chúng tôi cần biết vị trí của bạn.',
    allow: 'Cho phép truy cập vị trí',
    deny: 'Từ chối truy cập vị trí',
    allowDescription: 'Cho phép truy cập vị trí để tìm các sự kiện gần bạn.',
    denyDescription: 'Từ chối truy cập vị trí để tìm các sự kiện gần bạn.',
    manualLocation: 'Nhập vị trí của bạn',
  },
  locationInput: {
    title: 'Nhập vị trí của bạn',
    description: 'Để tìm các sự kiện gần bạn, chúng tôi cần biết vị trí của bạn.',
    useMyLocation: 'Sử dụng vị trí hiện tại',
    enterLocation: 'Nhập vị trí',
  },
  sports: {
    badminton: 'Cầu lông',
    pickleball: 'Pickleball',
    soccer: 'Bóng đá',
    tennis: 'Quần vợt',
    tableTennis: 'Bóng bàn',
    golf: 'Golf',
    volleyball: 'Bóng chuyền',
    basketball: 'Bóng rổ',
    swimming: 'Bơi lội',
    running: 'Chạy bộ',
    otherSports: 'Môn thể thao khác',
    selectSports: 'Chọn môn thể thao của bạn',
    selectSportsDescription: 'Chọn các môn thể thao bạn muốn chơi.',
  },
  home: {
    welcomeBack: 'Chào mừng trở lại!',
  },
}; 