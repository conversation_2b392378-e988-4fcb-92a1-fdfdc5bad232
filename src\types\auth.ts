export type UserRole = 'USER' | 'ADMIN';

export interface IUser {
  id: string;
  firebaseUid: string;
  email: string;
  displayName: string | null;
  photoURL: string | null;
  phoneNumber: string | null;
  emailVerified: boolean;
  phoneVerified: boolean;
  role: UserRole;
  provider: string;
  lastSignIn: string | null;
  createdAt: string;
  updatedAt: string;
  metadata?: any; // BE hỗ trợ metadata field
}

export interface IBaseResponse<T> {
  success: true;
  message: string;
  data: T;
}

export interface IErrorResponse {
  success: false;
  error: {
    message: string;
    status: number;
  };
}

export interface ILoginRequest {
  idToken: string;
}

export interface LoginResponseData {
  user: IUser;
  customToken: string;
  idToken?: string;
  refreshToken: string;
}

export interface ILoginResponse {
  success: true;
  message: string;
  data: LoginResponseData;
}

export interface IRegisterRequest {
  idToken: string;
  displayName?: string | null;
  phoneNumber?: string | null;
  metadata?: any;
}

export interface RegisterResponseData {
  user: IUser;
  customToken: string;
  idToken?: string;
  refreshToken: string;
}

export interface IRegisterResponse {
  success: true;
  message: string;
  data: RegisterResponseData;
}

export interface ProfileResponseData {
  user: IUser;
}

export interface IProfileResponse {
  success: true;
  message: string;
  data: ProfileResponseData;
}

export interface IUpdateProfileRequest {
  displayName?: string | null;
  phoneNumber?: string | null;
  phoneVerified?: boolean;
  metadata?: any;
}

export interface IRefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponseData {
  idToken: string;
  refreshToken: string;
  expiresIn: string;
}

export interface IRefreshTokenResponse {
  success: true;
  message: string;
  data: RefreshTokenResponseData;
}

export interface IUpdateRoleRequest {
  firebaseUid: string;
  role: UserRole;
}

export interface UpdateRoleResponseData {
  user: {
    id: string;
    firebaseUid: string;
    email: string;
    role: UserRole;
  };
}

export interface IUpdateRoleResponse {
  success: true;
  message: string;
  data: UpdateRoleResponseData;
}

export interface ILogin {
  email: string;
  password: string;
}

export interface IOAuthLogin {
  provider: string;
  idToken: string;
}

export interface ISignup {
  email: string;
  password: string;
  displayName?: string;
  metadata?: any;
}

export interface ISignupVerification {
  registrationId: string;
  otp: string;
}

export interface ISignupResendOtp {
  registrationId: string;
}

export interface IPasswordChange {
  oldPassword: string;
  newPassword: string;
}

export interface IPasswordReset {
  passwordResetId: string;
  newPassword: string;
}

export interface IPasswordForgot {
  email: string;
}

export interface IOtpVerification {
  passwordResetId: string;
  otp: string;
}

export interface IResendOtp {
  passwordResetId: string;
}

export interface IAuthToken {
  accessToken: string;
  expiresIn: number;
  refreshToken: string;
  refreshExpiresIn: number;
  tokenType: string;
  isFirstTime: boolean;
}

export interface IAuthResponse {
  user: IUser;
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
}

export interface IProfileUpdate {
  displayName?: string;
  phoneNumber?: string;
  phoneVerified?: boolean;
}

export interface AuthResponse {
  data: LoginResponseData;
}
