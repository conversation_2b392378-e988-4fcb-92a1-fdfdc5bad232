// This is just an example,
// so you can safely delete all default props below

export default {
  failed: 'Action failed',
  success: 'Action was successful',
  authentication: {
    signIn: 'Sign In',
    description: 'Welcome back! Please sign in to continue.',
    email: 'Email',
    password: 'Password',
    enterName: 'Enter your name',
    enterEmail: 'Enter your email',
    enterPassword: 'Enter your password',
    forgotPassword: 'Forgot Password?',
    dontHaveAccount: 'Don\'t have an account?',
    or: 'Or continue with',
    signInWithGoogle: 'Sign In with Google',
    signInWithApple: 'Sign In with Apple',
    signInWithFacebook: 'Sign In with Facebook',

    signUp: 'Create Account',
    signUpDescription: 'Join our community to get started.',
    alreadyHaveAccount: 'Already have an account?',
    agreeToTerms: 'By creating an account, you agree to our',
    termsOfService: 'Terms & Conditions',
    and: 'and',
    privacyPolicy: 'Privacy Policy',
  },
  profileSetup: {
    profile: {
      title: 'Complete your profile',
      description: 'Dont worry, only you can see your personal data. No one else will be able to see it.',
      name: 'Name',
      email: 'Email',
      complete: 'Complete',
    },
  },
  locationPermission: {
    title: 'What is your location?',
    description: 'To find nearby events, we need to know your location.',
    allow: 'Allow location access',
    deny: 'Deny location access',
    allowDescription: 'Allow location access to find nearby events.',
    denyDescription: 'Deny location access to find nearby events.',
    manualLocation: 'Enter your location',

  },
  locationInput: {
    title: 'Enter your location',
    description: 'To find nearby events, we need to know your location.',
    useMyLocation: 'Use my current location',
    enterLocation: 'Enter location',
  },
  sports: {
    badminton: 'Badminton',
    pickleball: 'Pickleball',
    soccer: 'Soccer',
    tennis: 'Tennis',
    tableTennis: 'Table Tennis',
    golf: 'Golf',
    volleyball: 'Volleyball',
    basketball: 'Basketball',
    swimming: 'Swimming',
    running: 'Running',
    otherSports: 'Other sports',
    selectSports: 'Select your sports',
    selectSportsDescription: 'Select the sports you want to play.',
  },
  home: {
    welcomeBack: 'Welcome back!',
  },
};
