<template>
  <div class="phone-input-container">
    <q-input
      v-model="phoneNumber"
      :placeholder="placeholder"
      outlined
      type="tel"
      class="full-width"
      :rules="validationRules"
      :error="hasError"
      :error-message="errorMessage"
      @update:model-value="onPhoneChange"
      @blur="validatePhone"
    >
      <template v-slot:prepend>
        <q-btn
          flat
          dense
          no-caps
          rounded
          class="country-selector"
          @click="showCountryDialog = true"
        >
          <div class="row items-center no-wrap">
            <span class="text-body2">{{ selectedCountry?.flag }}</span>
            <span class="q-ml-xs text-body2">{{ selectedCountry?.dialCode }}</span>
            <q-icon name="keyboard_arrow_down" size="18px" class="q-ml-xs" />
          </div>
        </q-btn>
      </template>
    </q-input>

    <!-- Country Selection Dialog -->
    <q-dialog v-model="showCountryDialog" position="bottom">
      <q-card class="full-width">
        <q-card-section class="q-pa-md">
          <div class="text-h6 q-mb-md">Select Country</div>
          <q-input
            v-model="searchQuery"
            placeholder="Search country..."
            outlined
            dense
            class="q-mb-md"
          >
            <template v-slot:prepend>
              <q-icon name="search" />
            </template>
          </q-input>
          <q-list class="country-list">
            <q-item
              v-for="country in filteredCountries"
              :key="country.code"
              clickable
              @click="selectCountry(country)"
              class="country-item"
            >
              <q-item-section avatar>
                <span class="text-h6">{{ country.flag }}</span>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ country.name }}</q-item-label>
                <q-item-label caption>{{ country.dialCode }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { isValidPhoneNumber, parsePhoneNumber } from 'libphonenumber-js';
import { computed, ref, watch } from 'vue';

interface Country {
  code: string;
  name: string;
  dialCode: string;
  flag: string;
  label: string;
}

interface Props {
  modelValue?: string;
  placeholder?: string;
  defaultCountry?: string;
  rules?: Array<(val: string) => boolean | string>;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'update:isValid', isValid: boolean): void;
  (e: 'update:countryCode', countryCode: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Enter Phone Number',
  defaultCountry: 'VN',
});

const emit = defineEmits<Emits>();

// Popular countries list with flags
const countries: Country[] = [
  { code: 'VN', name: 'Vietnam', dialCode: '+84', flag: '🇻🇳', label: '🇻🇳 +84' },
  { code: 'US', name: 'United States', dialCode: '+1', flag: '🇺🇸', label: '🇺🇸 +1' },
  { code: 'GB', name: 'United Kingdom', dialCode: '+44', flag: '🇬🇧', label: '🇬🇧 +44' },
  { code: 'CN', name: 'China', dialCode: '+86', flag: '🇨🇳', label: '🇨🇳 +86' },
  { code: 'JP', name: 'Japan', dialCode: '+81', flag: '🇯🇵', label: '🇯🇵 +81' },
  { code: 'KR', name: 'South Korea', dialCode: '+82', flag: '🇰🇷', label: '🇰🇷 +82' },
  { code: 'SG', name: 'Singapore', dialCode: '+65', flag: '🇸🇬', label: '🇸🇬 +65' },
  { code: 'TH', name: 'Thailand', dialCode: '+66', flag: '🇹🇭', label: '🇹🇭 +66' },
  { code: 'MY', name: 'Malaysia', dialCode: '+60', flag: '🇲🇾', label: '🇲🇾 +60' },
  { code: 'ID', name: 'Indonesia', dialCode: '+62', flag: '🇮🇩', label: '🇮🇩 +62' },
  { code: 'PH', name: 'Philippines', dialCode: '+63', flag: '🇵🇭', label: '🇵🇭 +63' },
  { code: 'IN', name: 'India', dialCode: '+91', flag: '🇮🇳', label: '🇮🇳 +91' },
  { code: 'AU', name: 'Australia', dialCode: '+61', flag: '🇦🇺', label: '🇦🇺 +61' },
  { code: 'CA', name: 'Canada', dialCode: '+1', flag: '🇨🇦', label: '🇨🇦 +1' },
  { code: 'DE', name: 'Germany', dialCode: '+49', flag: '🇩🇪', label: '🇩🇪 +49' },
  { code: 'FR', name: 'France', dialCode: '+33', flag: '🇫🇷', label: '🇫🇷 +33' },
];

const selectedCountry = ref<Country>(
  countries.find((c) => c.code === props.defaultCountry) || countries[0]!,
);
const phoneNumber = ref('');
const hasError = ref(false);
const errorMessage = ref('');
const showCountryDialog = ref(false);
const searchQuery = ref('');

const filteredCountries = computed(() => {
  if (!searchQuery.value) return countries;

  const query = searchQuery.value.toLowerCase();
  return countries.filter(
    (country) =>
      country.name.toLowerCase().includes(query) ||
      country.dialCode.includes(query) ||
      country.code.toLowerCase().includes(query),
  );
});

const placeholder = computed(() => {
  if (selectedCountry.value?.code) {
    // Generate example placeholder based on country
    const examples: Record<string, string> = {
      VN: '912 345 678',
      US: '(*************',
      GB: '7911 123456',
      CN: '138 0013 8000',
      JP: '90-1234-5678',
      KR: '010-1234-5678',
      SG: '8123 4567',
      TH: '************',
      MY: '************',
      ID: '812-3456-7890',
      PH: '************',
      IN: '98765 43210',
      AU: '0412 345 678',
      CA: '(*************',
      DE: '0151 23456789',
      FR: '06 12 34 56 78',
    };
    return examples[selectedCountry.value.code] || props.placeholder;
  }
  return props.placeholder;
});

const validationRules = computed(() => {
  const rules = [...(props.rules || [])];

  rules.push((val: string) => {
    if (!val) return 'Phone number is required';

    try {
      const fullNumber = selectedCountry.value.dialCode + val.replace(/\D/g, '');
      const isValid = isValidPhoneNumber(fullNumber, selectedCountry.value.code as any);

      if (!isValid) {
        return `Please enter a valid ${selectedCountry.value.name} phone number`;
      }

      return true;
    } catch (error) {
      console.log(error, 'Failed to validate phone number');
      return 'Please enter a valid phone number';
    }
  });

  return rules;
});

const validatePhone = () => {
  if (!phoneNumber.value) {
    hasError.value = true;
    errorMessage.value = 'Phone number is required';
    emit('update:isValid', false);
    return false;
  }

  try {
    const fullNumber = selectedCountry.value.dialCode + phoneNumber.value.replace(/\D/g, '');
    const isValid = isValidPhoneNumber(fullNumber, selectedCountry.value.code as any);

    hasError.value = !isValid;
    errorMessage.value = isValid
      ? ''
      : `Please enter a valid ${selectedCountry.value.name} phone number`;
    emit('update:isValid', isValid);

    return isValid;
  } catch (error) {
    console.log(error, 'Failed to validate phone number');
    hasError.value = true;
    errorMessage.value = 'Please enter a valid phone number';
    emit('update:isValid', false);
    return false;
  }
};

const selectCountry = (country: Country) => {
  selectedCountry.value = country;
  emit('update:countryCode', country.code);
  showCountryDialog.value = false;
  searchQuery.value = '';

  // Re-validate phone number with new country
  if (phoneNumber.value) {
    validatePhone();
  }

  // Update the full phone number
  updateModelValue();
};

const onPhoneChange = (value: string | number | null) => {
  const stringValue = value?.toString() || '';
  phoneNumber.value = stringValue;
  updateModelValue();

  // Clear error when user starts typing
  if (hasError.value && stringValue) {
    hasError.value = false;
    errorMessage.value = '';
  }
};

const updateModelValue = () => {
  if (phoneNumber.value) {
    const fullNumber = selectedCountry.value.dialCode + phoneNumber.value.replace(/\D/g, '');
    emit('update:modelValue', fullNumber);
  } else {
    emit('update:modelValue', '');
  }
};

// Watch for external model value changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (
      newValue &&
      newValue !== selectedCountry.value.dialCode + phoneNumber.value.replace(/\D/g, '')
    ) {
      try {
        const parsed = parsePhoneNumber(newValue);
        if (parsed) {
          const country = countries.find((c) => c.code === parsed.country);
          if (country) {
            selectedCountry.value = country;
          }
          phoneNumber.value = parsed.nationalNumber;
        }
      } catch (error) {
        // If parsing fails, just set the raw value
        console.log(error, 'Failed to parse phone number');
        phoneNumber.value = newValue;
      }
    }
  },
  { immediate: true },
);

// Expose validation method
defineExpose({
  validate: validatePhone,
  isValid: computed(() => !hasError.value && phoneNumber.value !== ''),
  fullPhoneNumber: computed(() => {
    if (phoneNumber.value) {
      return selectedCountry.value.dialCode + phoneNumber.value.replace(/\D/g, '');
    }
    return '';
  }),
});
</script>

<style scoped>
.phone-input-container {
  width: 100%;
}

.country-selector {
  min-width: 80px;
  border-right: 1px solid #e0e0e0;
  margin-right: 8px;
  padding-right: 8px;
}

.country-list {
  max-height: 300px;
  overflow-y: auto;
}

.country-item:hover {
  background-color: #f5f5f5;
}

:deep(.q-field__control) {
  height: 56px;
}

:deep(.q-field__prepend) {
  padding-right: 0;
}
</style>
