import type { HealthCheckResponse } from 'src/types/api';

import { apiService } from './api';

export class HealthService {
  /**
   * Check the health status of the backend API
   * @returns Promise<HealthCheckResponse>
   */
  async checkHealth(): Promise<HealthCheckResponse> {
    try {
      const response = await apiService.get<HealthCheckResponse>('/health');

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Health check failed');
      }
    } catch (error) {
      console.error('[HealthService] Health check failed:', error);

      // Return a fallback response for offline/error scenarios
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Check if the API is reachable
   * @returns Promise<boolean>
   */
  async isApiReachable(): Promise<boolean> {
    try {
      const health = await this.checkHealth();
      return health.status === 'ok';
    } catch (error) {
      console.error('[HealthService] API unreachable:', error);
      return false;
    }
  }

  /**
   * Get API status with additional information
   * @returns Promise with detailed status information
   */
  async getApiStatus(): Promise<{
    isOnline: boolean;
    health: HealthCheckResponse;
    baseUrl: string;
  }> {
    const health = await this.checkHealth();
    const isOnline = health.status === 'ok';

    return {
      isOnline,
      health,
      baseUrl: apiService.getBaseURL(),
    };
  }
}

// Create and export a singleton instance
export const healthService = new HealthService();
