import { Loading, QSpinnerTail } from 'quasar';

export async function withLoading<T>(
  action: () => Promise<T>,
  options: {
    message: string;
    onError?: (err: any) => void;
  },
): Promise<T | undefined> {
  Loading.show({
    message: options.message,
    spinner: QSpinnerTail,
    spinnerColor: 'primary',
    spinnerSize: 80,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  });

  try {
    return await action();
  } catch (err) {
    options.onError?.(err);
    console.error(err);
  } finally {
    Loading.hide();
  }
}
