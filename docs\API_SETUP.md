# API Handler Setup

This document describes the base API handler implementation for the Tido mobile application.

## Overview

The API handler provides a robust, configurable foundation for making HTTP requests to the backend API. It includes:

- Environment-based configuration
- Request/response interceptors
- Error handling
- Authentication token management
- Health check functionality
- Vue composables for reactive API usage

## Configuration

### Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:5000/api

# Environment
NODE_ENV=development
```

### Files Structure

```
src/
├── services/
│   ├── api.ts          # Base API service class
│   └── health.ts       # Health check service
├── types/
│   └── api.ts          # API type definitions
├── composables/
│   └── useApi.ts       # Vue composables for API usage
├── components/
│   └── ApiHealthCheck.vue  # Health check component
├── pages/
│   └── ApiDemoPage.vue     # API demo page
└── boot/
    └── axios.ts        # Updated axios boot file
```

## Usage Examples

### 1. Using the API Service Directly

```typescript
import { apiService } from 'src/services/api';

// GET request
const response = await apiService.get<User[]>('/users');

// POST request
const newUser = await apiService.post<User>('/users', {
  name: 'John Doe',
  email: '<EMAIL>',
});

// PUT request
const updatedUser = await apiService.put<User>(`/users/${id}`, userData);

// DELETE request
await apiService.delete(`/users/${id}`);
```

### 2. Using the Composable (Recommended)

```typescript
import { useApi } from 'src/composables/useApi';

const { data, loading, error, get, post } = useApi<User[]>();

// Make a GET request
await get('/users');

// Access reactive state
console.log(data.value); // User[] | null
console.log(loading.value); // boolean
console.log(error.value); // ApiError | null
```

### 3. Health Check Usage

```typescript
import { healthService } from 'src/services/health';

// Simple health check
const health = await healthService.checkHealth();
console.log(health.status); // 'ok' | 'error'

// Check if API is reachable
const isOnline = await healthService.isApiReachable();

// Get detailed status
const status = await healthService.getApiStatus();
```

### 4. Using in Vue Components

```vue
<template>
  <div>
    <q-spinner v-if="loading" />
    <div v-else-if="error">Error: {{ error.message }}</div>
    <div v-else>
      <div v-for="user in data" :key="user.id">
        {{ user.name }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useApi } from 'src/composables/useApi';
import { onMounted } from 'vue';

const { data, loading, error, get } = useApi();

onMounted(async () => {
  await get('/users');
});
</script>
```

## Features

### Authentication

The API service automatically handles authentication tokens:

```typescript
// Set auth token (usually after login)
apiService.setAuthToken('your-jwt-token');

// Clear auth token (usually after logout)
apiService.clearAuthToken();
```

### Error Handling

All API errors are consistently formatted:

```typescript
type ApiError = {
  message: string;
  code?: string | number;
  details?: unknown;
};
```

### Request Interceptors

- Automatically adds authentication headers
- Logs all requests in development
- Handles common error scenarios (401, network errors, etc.)

### Response Interceptors

- Logs responses in development
- Automatically handles 401 unauthorized responses
- Formats errors consistently

## Health Check Component

The `ApiHealthCheck` component provides a visual interface for monitoring API health:

- Real-time health status
- Base URL display
- Manual health check trigger
- Detailed status information
- Error display

## Demo Page

Visit the API demo page to test the implementation:

- Health check functionality
- Manual API endpoint testing
- Configuration information
- Usage examples

## Backend Requirements

Your backend should implement a health check endpoint:

```
GET /api/health

Response:
{
  "success": true,
  "data": {
    "status": "ok",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "version": "1.0.0",
    "uptime": 3600
  }
}
```

## Error Response Format

All API responses should follow this format:

```typescript
// Success response
{
  "success": true,
  "data": any,
  "message"?: string
}

// Error response
{
  "success": false,
  "error": string,
  "message"?: string
}
```

## Development

To test the API handler:

1. Start your backend server on `http://localhost:5000`
2. Run the Quasar development server: `npm run dev`
3. Navigate to the API demo page
4. Test the health check and other API functionality

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure your backend allows requests from your frontend domain
2. **Network Errors**: Check that the API base URL is correct in your `.env` file
3. **401 Errors**: Verify authentication token is set correctly
4. **Timeout Errors**: Increase timeout in the API service configuration

### Debug Mode

Enable debug logging by setting the log level in your browser console:

```javascript
localStorage.setItem('debug', 'api:*');
```
