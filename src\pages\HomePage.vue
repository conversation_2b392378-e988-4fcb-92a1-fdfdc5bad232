<template>
  <q-page class="q-pa-md">
    <LanguageSwitcher />
    <!-- User Info Header with Loading State -->
    <q-card class="q-my-md">
      <q-card-section class="row items-center justify-between">
        <div class="col-grow">
          <div class="text-h6">{{ t('home.welcomeBack') }}</div>
          <div class="text-subtitle2 text-grey-6">
            {{ userStore.user?.displayName || userStore.user?.email || 'Loading...' }}
          </div>
        </div>

        <div class="col-auto">
          <q-btn round color="negative" icon="logout" @click="handleLogout" :loading="auth.loading">
            <q-tooltip>Logout</q-tooltip>
          </q-btn>
        </div>
      </q-card-section>
    </q-card>

    <!-- Sport Slide -->

    <!-- Error Dialog -->
    <q-dialog v-model="showError" persistent>
      <q-card>
        <q-card-section>
          <div class="text-h6">Profile Error</div>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="Close" color="secondary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import LanguageSwitcher from 'src/components/LanguageSwitcher.vue';
// import type { EventCardData } from 'src/components/common/CardItem.vue';
import { useAuthStore } from 'src/stores/authStore';
import { useUserStore } from 'src/stores/userStore';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

// const eventItems: EventCardData[] = [
//   {
//     image: 'https://cdn.quasar.dev/img/parallax1.jpg',
//     badgeLabel: 'Intermediate',
//     title: 'Acoustic Serenade Showcase',
//     location: 'New York, USA',
//     time: 'May 29 - 10PM - 12PM',
//     price: '$30.00',
//   },
//   {
//     image: 'https://cdn.quasar.dev/img/parallax2.jpg',
//     badgeLabel: 'Advanced',
//     title: 'Night Jazz Festival',
//     location: 'Los Angeles, USA',
//     time: 'June 10 - 8PM - 11PM',
//     price: '$45.00',
//   },
// ];

const { t } = useI18n();

const auth = useAuthStore();

const $q = useQuasar();
const router = useRouter();

const showError = ref(false);
const userStore = useUserStore();

// Handle logout
const handleLogout = () => {
  try {
    $q.dialog({
      title: 'Confirm Logout',
      message: 'Are you sure you want to logout?',
      cancel: true,
      persistent: true,
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
    }).onOk(async () => {
      await auth.clearAuth();
      // Redirect to login
      await router.replace('/auth/login');

      $q.notify({
        type: 'positive',
        message: 'Logged out successfully',
        position: 'top',
      });
    });
  } catch (err) {
    console.error('Logout failed:', err);
    $q.notify({
      type: 'negative',
      message: 'Failed to logout',
      position: 'top',
    });
  }
};
</script>

<style lang="scss" scoped>
.my-card {
  width: 100%;
}

.skeleton-loading {
  background: linear-gradient(
    90deg,
    transparent 25%,
    rgba(255, 255, 255, 0.5) 50%,
    transparent 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}
</style>
