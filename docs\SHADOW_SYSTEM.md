# Tido Shadow System Documentation

This document explains how to use the custom shadow system implemented in your Quasar application.

## Overview

The shadow system provides a consistent and flexible way to add shadows to your components with support for:

- Multiple elevation levels
- Dark mode compatibility
- Interactive hover effects
- Focus states
- Custom branded shadows

## Shadow Variables

### Basic Shadow Colors

```scss
$shadow-color-light: rgba(0, 0, 0, 0.1);
$shadow-color-medium: rgba(0, 0, 0, 0.15);
$shadow-color-dark: rgba(0, 0, 0, 0.25);
$shadow-color-primary: rgba(255, 129, 66, 0.3); // Based on your primary color
```

### Shadow Elevations

```scss
$shadow-1:
  0 1px 3px $shadow-color-light,
  0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-2:
  0 4px 6px -1px $shadow-color-light,
  0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-3:
  0 10px 15px -3px $shadow-color-medium,
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-4:
  0 20px 25px -5px $shadow-color-medium,
  0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-5: 0 25px 50px -12px $shadow-color-dark;
```

## Utility Classes

### Basic Shadow Classes

```html
<!-- Light shadows for subtle elevation -->
<q-card class="shadow-sm">Subtle shadow</q-card>
<q-card class="shadow">Default shadow</q-card>
<q-card class="shadow-md">Medium shadow</q-card>
<q-card class="shadow-lg">Large shadow</q-card>
<q-card class="shadow-xl">Extra large shadow</q-card>
```

### Special Shadow Classes

```html
<!-- Inner shadow for pressed/inset effect -->
<div class="shadow-inner">Inner shadow</div>

<!-- Outline shadow for focus states -->
<div class="shadow-outline">Outline shadow</div>

<!-- Remove shadows -->
<div class="shadow-none">No shadow</div>
```

### Interactive Shadow Classes

```html
<!-- Hover effects -->
<q-card class="shadow-hover">Hover to see shadow change</q-card>
<q-card class="shadow-hover-lg">Large hover effect</q-card>

<!-- Component-specific shadows -->
<q-card class="card-shadow">Card with hover effect</q-card>
<q-btn class="button-shadow">Button with shadow states</q-btn>
<q-dialog class="modal-shadow">Modal with large shadow</q-dialog>
<q-menu class="dropdown-shadow">Dropdown with medium shadow</q-menu>
```

### Custom Tido Components

```html
<!-- Pre-styled components with your branding -->
<div class="tido-card">Branded card component</div>
<q-btn class="tido-button">Branded button</q-btn>
<q-input class="tido-input">Branded input with focus shadow</q-input>
```

## SCSS Mixins

### Shadow Elevation Mixin

```scss
.my-component {
  @include shadow-elevation(3); // Apply shadow level 3
}
```

### Shadow Hover Mixin

```scss
.my-card {
  @include shadow-hover(1, 3); // Start with level 1, hover to level 3
}
```

### Shadow Focus Mixin

```scss
.my-input {
  @include shadow-focus(); // Uses primary color
  // or
  @include shadow-focus(rgba(255, 0, 0, 0.3)); // Custom color
}
```

## Usage Examples

### In Vue Templates

```vue
<template>
  <!-- Basic usage -->
  <q-card class="shadow-md q-pa-md">
    <div>Card with medium shadow</div>
  </q-card>

  <!-- Interactive card -->
  <q-card class="card-shadow q-pa-md cursor-pointer" @click="handleClick">
    <div>Clickable card with hover effect</div>
  </q-card>

  <!-- Button with custom shadow -->
  <q-btn class="tido-button" color="primary" @click="submit"> Submit </q-btn>
</template>
```

### In Component Styles

```vue
<style lang="scss" scoped>
.my-custom-component {
  @include shadow-hover(2, 4);
  border-radius: 8px;
  transition: all 0.3s ease;

  &.active {
    @include shadow-elevation(5);
  }
}

.my-input-field {
  @include shadow-focus($primary);

  &.error {
    @include shadow-focus($negative);
  }
}
</style>
```

### Direct Variable Usage

```scss
.special-component {
  box-shadow: $shadow-3;

  &:hover {
    box-shadow: $shadow-4;
  }

  .body--dark & {
    box-shadow: $shadow-dark-3;
  }
}
```

## Dark Mode Support

The shadow system automatically adjusts for dark mode:

```scss
// Light mode
.shadow {
  box-shadow: $shadow-2;
}

// Dark mode (automatically applied)
.body--dark .shadow {
  box-shadow: $shadow-dark-2;
}
```

## Best Practices

### 1. Use Appropriate Elevation Levels

- **Level 1-2**: Buttons, small cards, inputs
- **Level 3**: Cards, panels, navigation
- **Level 4**: Floating action buttons, tooltips
- **Level 5**: Modals, dialogs, large overlays

### 2. Consistent Hover Effects

```html
<!-- Good: Consistent elevation increase -->
<q-card class="shadow-hover">Card</q-card>

<!-- Better: Use component-specific classes -->
<q-card class="card-shadow">Card</q-card>
```

### 3. Focus States for Accessibility

```scss
.interactive-element {
  @include shadow-focus(); // Always include focus states
}
```

### 4. Performance Considerations

```scss
// Good: Use transitions for smooth effects
.my-component {
  transition: box-shadow 0.3s ease;
  @include shadow-elevation(1);

  &:hover {
    @include shadow-elevation(2);
  }
}
```

## Customization

### Adding New Shadow Levels

```scss
// In quasar.variables.scss
$shadow-6: 0 35px 60px -15px rgba(0, 0, 0, 0.3);

// In app.scss
.shadow-xxl {
  box-shadow: $shadow-6;
}
```

### Custom Shadow Colors

```scss
// Brand-specific shadows
$shadow-success: 0 4px 6px rgba($positive, 0.3);
$shadow-warning: 0 4px 6px rgba($warning, 0.3);
$shadow-error: 0 4px 6px rgba($negative, 0.3);
```

### Component-Specific Shadows

```scss
.product-card {
  @include shadow-hover(1, 3);

  &.featured {
    box-shadow: 0 8px 25px rgba($primary, 0.4);
  }
}
```

## Testing Your Shadows

Use the `ShadowExamples.vue` component to test and preview all shadow styles:

```bash
# Add to your router or import in a page
import ShadowExamples from 'src/components/ShadowExamples.vue'
```

This component demonstrates all shadow utilities and provides a visual reference for your design system.
