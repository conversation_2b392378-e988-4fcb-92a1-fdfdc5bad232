<template>
  <div class="app-slider" :class="containerClass">
    <!-- Swiper container -->
    <div ref="swiperContainer" class="swiper">
      <div class="swiper-wrapper">
        <!-- Slides from slots -->
        <div v-for="(slide, index) in slides" :key="slide.id || index" class="swiper-slide">
          <slot name="slide" :slide="slide" :index="index">
            <!-- Default slide content -->
            <div class="slide-content">
              <img v-if="slide.image" :src="slide.image" :alt="slide.title || ''" class="slide-image" />
              <div v-if="slide.title || slide.description" class="slide-text">
                <h3 v-if="slide.title" class="slide-title">{{ slide.title }}</h3>
                <p v-if="slide.description" class="slide-description">{{ slide.description }}</p>
              </div>
              <div v-if="slide.content" v-html="slide.content"></div>
            </div>
          </slot>
        </div>
      </div>

      <!-- Navigation buttons -->
      <div v-if="showNavigation" class="swiper-button-next" :class="navigationClass"></div>
      <div v-if="showNavigation" class="swiper-button-prev" :class="navigationClass"></div>

      <!-- Pagination -->
      <div v-if="showPagination" class="swiper-pagination" :class="paginationClass"></div>

      <!-- Scrollbar -->
      <div v-if="showScrollbar" class="swiper-scrollbar" :class="scrollbarClass"></div>
    </div>

    <!-- Custom navigation buttons (outside swiper) -->
    <div v-if="externalNavigation" class="external-navigation">
      <q-btn
        round
        icon="chevron_left"
        color="primary"
        size="sm"
        @click="slidePrev"
        :disable="isBeginning"
        class="nav-btn nav-btn-prev"
      />
      <q-btn
        round
        icon="chevron_right"
        color="primary"
        size="sm"
        @click="slideNext"
        :disable="isEnd"
        class="nav-btn nav-btn-next"
      />
    </div>

    <!-- Custom pagination dots (outside swiper) -->
    <div v-if="externalPagination" class="external-pagination">
      <q-btn
        v-for="(slide, index) in slides"
        :key="`dot-${slide.id || index}`"
        round
        size="xs"
        :color="currentSlide === index ? 'primary' : 'grey-5'"
        @click="slideTo(index)"
        class="pagination-dot"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue';
import { Swiper } from 'swiper';
import { Navigation, Pagination, Scrollbar, Autoplay, EffectFade, EffectCube, EffectCoverflow, EffectFlip, EffectCards, EffectCreative, Mousewheel, Keyboard, FreeMode } from 'swiper/modules';
import type { SliderOptions, SliderSlide, SliderEvents } from '../../types/slider';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';
import 'swiper/css/autoplay';
import 'swiper/css/effect-fade';
import 'swiper/css/effect-cube';
import 'swiper/css/effect-coverflow';
import 'swiper/css/effect-flip';
import 'swiper/css/effect-cards';
import 'swiper/css/effect-creative';

interface Props {
  slides?: SliderSlide[];
  options?: SliderOptions;
  containerClass?: string;
  navigationClass?: string;
  paginationClass?: string;
  scrollbarClass?: string;
  externalNavigation?: boolean;
  externalPagination?: boolean;
  height?: string;
}

interface Emits {
  (e: 'slideChange', swiper: any, activeIndex: number): void;
  (e: 'slideChangeTransitionStart', swiper: any): void;
  (e: 'slideChangeTransitionEnd', swiper: any): void;
  (e: 'reachBeginning', swiper: any): void;
  (e: 'reachEnd', swiper: any): void;
  (e: 'click', swiper: any, event: MouseEvent): void;
  (e: 'tap', swiper: any, event: TouchEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  slides: () => [],
  options: () => ({}),
  containerClass: '',
  navigationClass: '',
  paginationClass: '',
  scrollbarClass: '',
  externalNavigation: false,
  externalPagination: false,
  height: 'auto',
});

const emit = defineEmits<Emits>();

// Refs
const swiperContainer = ref<HTMLElement>();
let swiperInstance: Swiper | null = null;

// State
const currentSlide = ref(0);
const isBeginning = ref(true);
const isEnd = ref(false);

// Computed properties
const showNavigation = computed(() => {
  return props.options.navigation === true || (typeof props.options.navigation === 'object' && props.options.navigation);
});

const showPagination = computed(() => {
  return props.options.pagination === true || (typeof props.options.pagination === 'object' && props.options.pagination);
});

const showScrollbar = computed(() => {
  return props.options.scrollbar === true || (typeof props.options.scrollbar === 'object' && props.options.scrollbar);
});

// Methods
const initSwiper = async () => {
  if (!swiperContainer.value) return;

  await nextTick();

  // Configure modules based on options
  const modules = [Navigation, Pagination, Scrollbar];
  
  if (props.options.autoplay) modules.push(Autoplay);
  if (props.options.effect === 'fade') modules.push(EffectFade);
  if (props.options.effect === 'cube') modules.push(EffectCube);
  if (props.options.effect === 'coverflow') modules.push(EffectCoverflow);
  if (props.options.effect === 'flip') modules.push(EffectFlip);
  if (props.options.effect === 'cards') modules.push(EffectCards);
  if (props.options.effect === 'creative') modules.push(EffectCreative);
  if (props.options.mousewheel) modules.push(Mousewheel);
  if (props.options.keyboard) modules.push(Keyboard);
  if (props.options.freeMode) modules.push(FreeMode);

  // Default options
  const defaultOptions: SliderOptions = {
    slidesPerView: 1,
    spaceBetween: 10,
    loop: false,
    grabCursor: true,
    ...props.options,
  };

  // Configure navigation
  if (showNavigation.value && !props.externalNavigation) {
    defaultOptions.navigation = {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
      ...(typeof props.options.navigation === 'object' ? props.options.navigation : {}),
    };
  }

  // Configure pagination
  if (showPagination.value && !props.externalPagination) {
    defaultOptions.pagination = {
      el: '.swiper-pagination',
      clickable: true,
      ...(typeof props.options.pagination === 'object' ? props.options.pagination : {}),
    };
  }

  // Configure scrollbar
  if (showScrollbar.value) {
    defaultOptions.scrollbar = {
      el: '.swiper-scrollbar',
      draggable: true,
      ...(typeof props.options.scrollbar === 'object' ? props.options.scrollbar : {}),
    };
  }

  // Initialize Swiper
  swiperInstance = new Swiper(swiperContainer.value, {
    modules,
    ...defaultOptions,
    on: {
      slideChange: (swiper) => {
        currentSlide.value = swiper.activeIndex;
        isBeginning.value = swiper.isBeginning;
        isEnd.value = swiper.isEnd;
        emit('slideChange', swiper, swiper.activeIndex);
      },
      slideChangeTransitionStart: (swiper) => emit('slideChangeTransitionStart', swiper),
      slideChangeTransitionEnd: (swiper) => emit('slideChangeTransitionEnd', swiper),
      reachBeginning: (swiper) => {
        isBeginning.value = true;
        emit('reachBeginning', swiper);
      },
      reachEnd: (swiper) => {
        isEnd.value = true;
        emit('reachEnd', swiper);
      },
      click: (swiper, event) => emit('click', swiper, event),
      tap: (swiper, event) => emit('tap', swiper, event),
    },
  });

  // Update initial state
  currentSlide.value = swiperInstance.activeIndex;
  isBeginning.value = swiperInstance.isBeginning;
  isEnd.value = swiperInstance.isEnd;
};

const destroySwiper = () => {
  if (swiperInstance) {
    swiperInstance.destroy(true, true);
    swiperInstance = null;
  }
};

// Navigation methods
const slideNext = () => swiperInstance?.slideNext();
const slidePrev = () => swiperInstance?.slidePrev();
const slideTo = (index: number) => swiperInstance?.slideTo(index);

// Expose methods for parent component
defineExpose({
  slideNext,
  slidePrev,
  slideTo,
  swiper: computed(() => swiperInstance),
  currentSlide: computed(() => currentSlide.value),
  isBeginning: computed(() => isBeginning.value),
  isEnd: computed(() => isEnd.value),
});

// Lifecycle
onMounted(() => {
  initSwiper();
});

onUnmounted(() => {
  destroySwiper();
});

// Watch for slides changes
watch(() => props.slides, () => {
  if (swiperInstance) {
    nextTick(() => {
      swiperInstance?.update();
    });
  }
}, { deep: true });

// Watch for options changes
watch(() => props.options, () => {
  destroySwiper();
  nextTick(() => {
    initSwiper();
  });
}, { deep: true });
</script>

<style scoped lang="scss">
.app-slider {
  position: relative;
  width: 100%;
  height: v-bind(height);

  .swiper {
    width: 100%;
    height: 100%;
  }

  .slide-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 1rem;
  }

  .slide-image {
    width: 100%;
    height: auto;
    max-height: 70%;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
  }

  .slide-text {
    text-align: center;
  }

  .slide-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--q-primary);
  }

  .slide-description {
    font-size: 1rem;
    color: var(--q-dark);
    line-height: 1.5;
  }

  // External navigation
  .external-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 10;

    .nav-btn {
      pointer-events: all;
      margin: 0 1rem;
    }
  }

  // External pagination
  .external-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;

    .pagination-dot {
      min-width: 12px;
      min-height: 12px;
      width: 12px;
      height: 12px;
    }
  }
}

// Override Swiper default styles
:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  color: var(--q-primary);
  
  &:after {
    font-size: 1.5rem;
  }
}

:deep(.swiper-pagination-bullet) {
  background-color: var(--q-primary);
  opacity: 0.3;

  &.swiper-pagination-bullet-active {
    opacity: 1;
  }
}

:deep(.swiper-scrollbar-drag) {
  background-color: var(--q-primary);
}
</style>
