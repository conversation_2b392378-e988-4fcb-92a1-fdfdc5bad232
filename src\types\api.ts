// Base API response structure
export type ApiResponse<T = unknown> = {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
};

// Health check response
export type HealthCheckResponse = {
  status: 'ok' | 'error';
  timestamp: string;
  version?: string;
  uptime?: number;
};

// API error structure
export type ApiError = {
  message: string;
  code?: string | number;
  details?: unknown;
};

// Request configuration
export type ApiRequestConfig = {
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
};
