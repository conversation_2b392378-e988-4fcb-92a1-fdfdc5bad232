<template>
  <q-card class="event-card">
    <q-img :src="data.image" class="rounded-borders q-mb-sm" fit="cover">
      <q-btn
        color="white"
        text-color="primary"
        :label="data.badgeLabel"
        class="absolute-top-left q-ma-sm"
      />

      <q-btn round icon="upload" color="grey-7" size="md" class="absolute-top-right q-ma-sm" />
    </q-img>

    <q-card-section class="column items-start justify-between no-border" style="gap: 10px">
      <div class="text-subtitle1 text-bold">{{ data.title }}</div>
      <div class="row items-center text-body1 text-grey-7" style="gap: 5px">
        <q-icon name="place" color="primary" size="20px" />
        {{ data.location }}
      </div>
      <div class="row items-center text-body1 text-grey-7" style="gap: 5px">
        <q-icon name="schedule" color="primary" size="20px" />
        {{ data.time }}
      </div>
    </q-card-section>

    <q-card-section class="row items-center justify-between no-border">
      <div class="text-primary text-h6 text-bold">
        {{ data.price }} <span class="text-grey-6 text-weight-light">/Person</span>
      </div>
      <q-btn
        color="primary"
        text-color="white"
        size="md"
        class="text-base"
        style="border-radius: 8px"
        :label="data.buttonText || 'Join now'"
      />
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
export interface EventCardData {
  image: string;
  badgeLabel?: string;
  title: string;
  location: string;
  time: string;
  price: string;
  buttonText?: string;
}

defineProps<{
  data: EventCardData;
}>();
</script>

<style scoped lang="scss">
.event-card {
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px !important;

  .upload-icon {
    border-radius: 100px;
    width: 50px;
    height: 50px;
    background-color: #fff;
    z-index: 1000;
  }
}
</style>
