import axios from 'axios';
import type { AxiosInstance } from 'axios';
import { boot } from 'quasar/wrappers';
import { LOCAL_STORAGE_KEYS } from 'src/constants';
import { useAuthStore } from 'src/stores/authStore';
import { useRouter } from 'vue-router';

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $axios: AxiosInstance;
  }
}

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL ?? 'http://localhost:5000',
  headers: {
    'Content-Type': 'application/json',
  },
});

const router = useRouter();

// Add request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(LOCAL_STORAGE_KEYS.TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// Add response interceptor
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // If error is 401 and we haven't tried to refresh token yet
    if (error.response?.status === 401) {
      await useAuthStore().clearAuth();
      router.push('/auth/login');
    }

    return Promise.reject(error);
  },
);

export default boot(({ app }) => {
  // for use inside Vue files (Options API) through this.$axios
  app.config.globalProperties.$axios = api;
});

export { api };
