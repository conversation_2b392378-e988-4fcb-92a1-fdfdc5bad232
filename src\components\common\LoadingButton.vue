<template>
  <q-btn v-bind="$attrs" :loading="loading" :disable="loading || disabled" @click="handleClick">
    <template #loading>
      <q-spinner-hourglass class="on-left" />
      {{ loadingText }}
    </template>
    <slot />
  </q-btn>
</template>

<script setup lang="ts">
interface Props {
  loading?: boolean;
  disabled?: boolean;
  loadingText?: string;
  asyncAction?: () => Promise<void>;
}

interface Emits {
  (e: 'click', event: Event): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  disabled: false,
  loadingText: 'Loading...',
});

const emit = defineEmits<Emits>();

async function handleClick(event: Event) {
  if (props.asyncAction) {
    try {
      await props.asyncAction();
    } catch (error) {
      console.error('Button action failed:', error);
    }
  } else {
    emit('click', event);
  }
}
</script>
