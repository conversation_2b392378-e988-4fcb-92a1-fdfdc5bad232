<template>
  <q-page class="column items-center justify-center q-pa-md">
    <div class="flex flex-center q-mb-lg">
      <q-avatar size="80px" color="orange-2">
        <q-icon name="place" size="56px" color="orange" />
      </q-avatar>
    </div>
    <div class="text-h6 text-center">What is Your Location?</div>
    <div class="text-body2 text-grey-6 text-center q-mb-lg">
      To find nearby events, share your location with us.
    </div>

    <!-- Actions -->
    <div class="full-width">
      <q-btn v-if="!locationGranted" color="primary" class="full-width q-my-lg" label="Allow location access"
        :loading="loading" size="lg" @click="requestLocation" />
      <q-btn color="primary" @click="skipNow" class="full-width" :label="'Skip for now'" :flat="!locationGranted" />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { useLocalStorage } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { LOCAL_STORAGE_KEYS } from 'src/constants';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const $q = useQuasar();
const router = useRouter();

// Location state
const locationGranted = ref(false);
const loading = ref(false);

const locationStorage = useLocalStorage(LOCAL_STORAGE_KEYS.LOCATION, {
  latitude: null as number | null,
  longitude: null as number | null,
});

// Location handling
const requestLocation = async () => {
  loading.value = true;
  try {
    const position = await new Promise<GeolocationPosition>((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject);
    });

    locationGranted.value = true;
    
    locationStorage.value = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
    };

    router.push('/onboarding/sports');
  } catch (error) {
    $q.notify({
      type: 'warning',
      message: 'Location access denied' + (error as Error).message,
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
}

const skipNow = () => {
  router.push('/onboarding/sports');
}
</script>
