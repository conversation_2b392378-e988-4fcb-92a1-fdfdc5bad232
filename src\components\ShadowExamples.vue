<template>
  <div class="q-pa-md">
    <div class="text-h4 q-mb-lg">Shadow Examples</div>

    <!-- Basic Shadow Classes -->
    <div class="text-h6 q-mb-md">Basic Shadow Classes</div>
    <div class="row q-gutter-md q-mb-xl">
      <q-card class="shadow-sm q-pa-md">
        <div class="text-subtitle2">shadow-sm</div>
        <div class="text-caption">Subtle shadow</div>
      </q-card>

      <q-card class="shadow q-pa-md">
        <div class="text-subtitle2">shadow</div>
        <div class="text-caption">Default shadow</div>
      </q-card>

      <q-card class="shadow-md q-pa-md">
        <div class="text-subtitle2">shadow-md</div>
        <div class="text-caption">Medium shadow</div>
      </q-card>

      <q-card class="shadow-lg q-pa-md">
        <div class="text-subtitle2">shadow-lg</div>
        <div class="text-caption">Large shadow</div>
      </q-card>

      <q-card class="shadow-xl q-pa-md">
        <div class="text-subtitle2">shadow-xl</div>
        <div class="text-caption">Extra large shadow</div>
      </q-card>
    </div>

    <!-- Interactive Shadows -->
    <div class="text-h6 q-mb-md">Interactive Shadows</div>
    <div class="row q-gutter-md q-mb-xl">
      <q-card class="card-shadow q-pa-md cursor-pointer">
        <div class="text-subtitle2">Hover Card</div>
        <div class="text-caption">Hover to see shadow change</div>
      </q-card>

      <q-btn class="button-shadow" color="primary" label="Button with Shadow" />

      <q-card class="shadow-hover q-pa-md cursor-pointer">
        <div class="text-subtitle2">Shadow Hover</div>
        <div class="text-caption">Generic hover effect</div>
      </q-card>
    </div>

    <!-- Custom Tido Components -->
    <div class="text-h6 q-mb-md">Custom Tido Components</div>
    <div class="row q-gutter-md q-mb-xl">
      <div class="tido-card q-pa-md">
        <div class="text-subtitle2">Tido Card</div>
        <div class="text-caption">Custom branded card with shadows</div>
      </div>

      <q-btn class="tido-button" color="primary" label="Tido Button" />

      <q-input class="tido-input" v-model="inputValue" label="Tido Input" outlined />
    </div>

    <!-- Special Shadows -->
    <div class="text-h6 q-mb-md">Special Shadows</div>
    <div class="row q-gutter-md q-mb-xl">
      <q-card class="shadow-inner q-pa-md">
        <div class="text-subtitle2">Inner Shadow</div>
        <div class="text-caption">Inset shadow effect</div>
      </q-card>

      <q-card class="shadow-outline q-pa-md">
        <div class="text-subtitle2">Outline Shadow</div>
        <div class="text-caption">Focus-style outline</div>
      </q-card>
    </div>

    <!-- Using Mixins in Component Style -->
    <div class="text-h6 q-mb-md">Component with Custom Mixins</div>
    <div class="custom-component q-pa-md q-mb-xl">
      <div class="text-subtitle2">Custom Component</div>
      <div class="text-caption">Using SCSS mixins for shadows</div>
    </div>

    <!-- Dark Mode Example -->
    <div class="text-h6 q-mb-md">Dark Mode Shadows</div>
    <!-- <q-btn
      @click="$q.dark.toggle()"
      :color="$q.dark.isActive ? 'yellow' : 'primary'"
      :label="$q.dark.isActive ? 'Light Mode' : 'Dark Mode'"
      class="q-mb-md"
    /> -->
    <div class="row q-gutter-md">
      <q-card class="shadow q-pa-md">
        <div class="text-subtitle2">Auto Dark Shadow</div>
        <div class="text-caption">Automatically adjusts for dark mode</div>
      </q-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const inputValue = ref('');
</script>

<style lang="scss" scoped>
.custom-component {
  @include shadow-hover(2, 4);
  background: white;
  border-radius: 8px;
  cursor: pointer;

  .body--dark & {
    background: $dark;
  }
}

// Example of using shadow variables directly
.example-direct-shadow {
  box-shadow: $shadow-3;

  &:hover {
    box-shadow: $shadow-4;
  }
}
</style>
