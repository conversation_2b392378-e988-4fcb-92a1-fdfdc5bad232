// src/composables/useApi.ts
import { Loading, QSpinnerDots } from 'quasar';
import { apiService } from 'src/services/api';
import type { ApiError, ApiResponse } from 'src/types/api';
import { type Ref, ref } from 'vue';

export type UseApiState<T> = {
  data: Ref<T | null>;
  loading: Ref<boolean>;
  error: Ref<ApiError | null>;
};

export type UseApiOptions = {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: ApiError) => void;
  useGlobalLoading?: boolean;
  loadingMessage?: string;
  spinnerColor?: string;
  spinnerSize?: number;
};

/**
 * Composable for making API requests with reactive state management
 */
export function useApi<T = any>() {
  const data = ref<T | null>(null);
  const loading = ref(false);
  const error = ref<ApiError | null>(null);

  const execute = async <R = T>(
    apiCall: () => Promise<ApiResponse<R>>,
    options: UseApiOptions = {},
  ): Promise<R | null> => {
    const {
      useGlobalLoading = false,
      loadingMessage = 'Loading...',
      spinnerColor = 'primary',
      spinnerSize = 80,
    } = options;

    loading.value = true;
    error.value = null;

    if (useGlobalLoading) {
      Loading.show({
        message: loadingMessage,
        spinner: QSpinnerDots,
        spinnerColor,
        spinnerSize,
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
      });
    }

    try {
      const response = await apiCall();

      if (response.success && response.data !== undefined) {
        data.value = response.data as T;
        options.onSuccess?.(response.data);
        return response.data;
      } else {
        const apiError: ApiError = {
          message: response.message || 'API request failed',
          code: 'API_ERROR',
          details: response,
        };
        error.value = apiError;
        options.onError?.(apiError);
        return null;
      }
    } catch (err) {
      const apiError = err as ApiError;
      error.value = apiError;
      options.onError?.(apiError);
      console.error('API request failed:', err);
      return null;
    } finally {
      loading.value = false;
      if (useGlobalLoading) {
        Loading.hide();
      }
    }
  };

  const get = async <R = T>(url: string, options: UseApiOptions = {}): Promise<R | null> => {
    return execute(() => apiService.get<R>(url), options);
  };

  const post = async <R = T>(
    url: string,
    payload?: any,
    options: UseApiOptions = {},
  ): Promise<R | null> => {
    return execute(() => apiService.post<R>(url, payload), options);
  };

  const put = async <R = T>(
    url: string,
    payload?: any,
    options: UseApiOptions = {},
  ): Promise<R | null> => {
    return execute(() => apiService.put<R>(url, payload), options);
  };

  const del = async <R = T>(url: string, options: UseApiOptions = {}): Promise<R | null> => {
    return execute(() => apiService.delete<R>(url), options);
  };

  const reset = () => {
    data.value = null;
    loading.value = false;
    error.value = null;
  };

  return {
    // State
    data,
    loading,
    error,

    // Methods
    execute,
    get,
    post,
    put,
    delete: del,
    reset,
  };
}
