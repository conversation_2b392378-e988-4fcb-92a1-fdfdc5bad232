<template>
  <q-page class="q-pa-md">
    <div class="row q-gutter-md">
      <div class="col-12">
        <h4 class="q-mt-none">API Handler Demo</h4>
        <p class="text-grey-7">
          This page demonstrates the base API handler functionality including health checks, error
          handling, and the composable API usage patterns.
        </p>
      </div>

      <!-- Health Check Component -->
      <div class="col-12 col-md-6">
        <api-health-check />
      </div>

      <!-- Manual API Testing -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">Manual API Testing</div>
          </q-card-section>

          <q-card-section>
            <q-input
              v-model="testEndpoint"
              label="Test Endpoint"
              hint="Enter an endpoint to test (e.g., /health, /users)"
              outlined
              class="q-mb-md"
            />

            <div class="row q-gutter-sm">
              <q-btn color="primary" :loading="apiLoading" @click="testGet"> GET Request </q-btn>
              <q-btn color="secondary" :loading="apiLoading" @click="testPost">
                POST Request
              </q-btn>
            </div>
          </q-card-section>

          <q-card-section v-if="apiResult">
            <div class="text-subtitle2 q-mb-sm">Response:</div>
            <q-scroll-area style="height: 200px">
              <pre class="api-response">{{ JSON.stringify(apiResult, null, 2) }}</pre>
            </q-scroll-area>
          </q-card-section>

          <q-card-section v-if="apiError">
            <q-banner class="text-white bg-red">
              <template #avatar>
                <q-icon name="error" color="white" />
              </template>
              <div class="text-subtitle2">Error:</div>
              <div>{{ apiError.message }}</div>
              <div v-if="apiError.code" class="text-caption">Code: {{ apiError.code }}</div>
            </q-banner>
          </q-card-section>
        </q-card>
      </div>

      <!-- API Configuration Info -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">API Configuration</div>
          </q-card-section>

          <q-card-section>
            <div class="row q-gutter-md">
              <div class="col">
                <q-item>
                  <q-item-section avatar>
                    <q-icon name="link" color="primary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>Base URL</q-item-label>
                    <q-item-label caption>{{ baseUrl }}</q-item-label>
                  </q-item-section>
                </q-item>
              </div>

              <div class="col">
                <q-item>
                  <q-item-section avatar>
                    <q-icon name="settings" color="secondary" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>Environment</q-item-label>
                    <q-item-label caption>{{ environment }}</q-item-label>
                  </q-item-section>
                </q-item>
              </div>
            </div>
          </q-card-section>

          <q-card-section>
            <q-expansion-item icon="code" label="Usage Examples" header-class="text-primary">
              <q-card>
                <q-card-section>
                  <div class="text-subtitle2 q-mb-sm">Using the API Service directly:</div>
                  <pre class="code-example">
import { apiService } from 'src/services/api';

// GET request
const response = await apiService.get('/users');

// POST request
const newUser = await apiService.post('/users', { name: 'John' });
                  </pre>

                  <div class="text-subtitle2 q-mb-sm q-mt-md">Using the composable:</div>
                  <pre class="code-example">
import { useApi } from 'src/composables/useApi';

const { data, loading, error, get } = useApi();

// Make a GET request
await get('/users');
                  </pre>

                  <div class="text-subtitle2 q-mb-sm q-mt-md">Using the health check service:</div>
                  <pre class="code-example">
import { healthService } from 'src/services/health';

// Check health
const health = await healthService.checkHealth();

// Get detailed status
const status = await healthService.getApiStatus();
                  </pre>
                </q-card-section>
              </q-card>
            </q-expansion-item>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import ApiHealthCheck from 'src/components/ApiHealthCheck.vue';
import { useApi } from 'src/composables/useApi';
import { apiService } from 'src/services/api';
import { onMounted, ref } from 'vue';

// Reactive state
const testEndpoint = ref('/health');
const baseUrl = ref('');
const environment = ref('');

// API testing
const { data: apiResult, loading: apiLoading, error: apiError, get, post } = useApi();

// Methods
const testGet = async () => {
  await get(testEndpoint.value);
};

const testPost = async () => {
  await post(testEndpoint.value, {
    test: true,
    timestamp: new Date().toISOString(),
  });
};

// Initialize
onMounted(() => {
  baseUrl.value = apiService.getBaseURL();
  environment.value = import.meta.env.NODE_ENV || 'development';
});
</script>

<style scoped>
.api-response,
.code-example {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.code-example {
  background-color: #2d3748;
  color: #e2e8f0;
}
</style>
