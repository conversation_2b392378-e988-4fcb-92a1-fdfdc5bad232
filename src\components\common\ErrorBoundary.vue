<template>
  <div v-if="hasError" class="error-boundary">
    <q-card class="q-ma-md">
      <q-card-section class="text-center">
        <q-icon name="error_outline" size="64px" color="negative" class="q-mb-md" />
        <div class="text-h6 q-mb-sm">{{ errorTitle }}</div>
        <div class="text-body2 text-grey-6 q-mb-md">{{ errorMessage }}</div>

        <div class="q-gutter-sm">
          <q-btn color="primary" label="Try Again" @click="retry" :loading="retrying" />
          <q-btn flat color="grey" label="Go Home" @click="goHome" />
        </div>

        <!-- Debug info in development -->
        <q-expansion-item
          v-if="isDevelopment && errorDetails"
          icon="bug_report"
          label="Debug Info"
          class="q-mt-md"
        >
          <q-card-section class="text-left">
            <pre class="text-caption">{{ errorDetails }}</pre>
          </q-card-section>
        </q-expansion-item>
      </q-card-section>
    </q-card>
  </div>

  <slot v-else />
</template>

<script setup lang="ts">
import { computed, onErrorCaptured, ref } from 'vue';
import { useRouter } from 'vue-router';

interface Props {
  errorTitle?: string;
  errorMessage?: string;
  showRetry?: boolean;
  onRetry?: () => void | Promise<void>;
}

const props = withDefaults(defineProps<Props>(), {
  errorTitle: 'Something went wrong',
  errorMessage: 'An unexpected error occurred. Please try again.',
  showRetry: true,
});

const router = useRouter();

const hasError = ref(false);
const retrying = ref(false);
const errorDetails = ref<string>('');

const isDevelopment = computed(() => process.env.NODE_ENV === 'development');

onErrorCaptured((error: Error) => {
  console.error('Error caught by boundary:', error);
  hasError.value = true;
  errorDetails.value = error.stack || error.message;
  return false; // Prevent error from propagating
});

async function retry() {
  if (props.onRetry) {
    retrying.value = true;
    try {
      await props.onRetry();
      hasError.value = false;
      errorDetails.value = '';
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      retrying.value = false;
    }
  } else {
    // Default retry: reload the page
    window.location.reload();
  }
}

function goHome() {
  router.push('/');
}

// Expose method to manually trigger error state
function triggerError(error: Error) {
  hasError.value = true;
  errorDetails.value = error.stack || error.message;
}

defineExpose({
  triggerError,
  reset: () => {
    hasError.value = false;
    errorDetails.value = '';
  },
});
</script>

<style scoped>
.error-boundary {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

pre {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}
</style>
