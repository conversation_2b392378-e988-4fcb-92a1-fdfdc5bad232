<template>
  <q-page class="column q-pa-md">
    <!-- Header -->
    <div class="q-my-md">
      <q-btn round outline icon=" arrow_back" @click="router.back()" />
    </div>

    <!-- Content Area - grows to fill available space -->
    <div class="col column q-mt-md">
      <div>
        <div class="text-h4 text-weight-bold q-mb-md">Which sports are you playing?</div>
        <div class="text-body2 text-weight-semibold text-grey-7 q-mb-lg">
          Personalize your sports by choosing your interests
        </div>
        <div class="row q-gutter-sm">
          <q-chip
            v-for="sport in availableSports"
            :key="sport.id"
            clickable
            size="lg"
            :color="selectedSports.includes(sport.id) ? 'primary' : 'grey-3'"
            :text-color="selectedSports.includes(sport.id) ? 'white' : 'black'"
            @click="toggleSport(sport.id)"
            :icon="sport.icon"
          >
            {{ sport.name }}
          </q-chip>
        </div>
      </div>

      <!-- Spacer to push button to bottom -->
      <div class="col"></div>

      <!-- Button Area -->
      <div class="q-mb-xl">
        <q-btn
          color="primary"
          class="full-width"
          label="Next"
          unelevated
          size="lg"
          rounded
          :loading="loading"
          :disable="selectedSports.length === 0 || loading"
          @click="handleSubmit"
        />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { useLocalStorage } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { LOCAL_STORAGE_KEYS } from 'src/constants';
import { useUserStore } from 'src/stores/userStore';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const $q = useQuasar();
const router = useRouter();

// Sports data
const availableSports = ref([
  { id: 'badminton', name: 'Badminton', category: 'racket', icon: 'mdi-badminton' },
  { id: 'pickleball', name: 'Pickle Ball', category: 'racket', icon: 'mdi-handball' },
  { id: 'soccer', name: 'Soccer', category: 'team', icon: 'sports_soccer' },
  { id: 'basketball', name: 'Basketball', category: 'team', icon: 'sports_basketball' },
  { id: 'golf', name: 'Golf', category: 'individual', icon: 'sports_golf' },
  { id: 'tennis', name: 'Tennis', category: 'racket', icon: 'sports_tennis' },
]);
const selectedSports = ref<string[]>([]);
const loading = ref(false);

// Sports handling
function toggleSport(sportId: string) {
  const index = selectedSports.value.indexOf(sportId);
  if (index === -1) {
    selectedSports.value.push(sportId);
  } else {
    selectedSports.value.splice(index, 1);
  }
}
const location = useLocalStorage<string | null>(LOCAL_STORAGE_KEYS.LOCATION, null);
// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
const profileSetup = useLocalStorage<any | null>(LOCAL_STORAGE_KEYS.PROFILE_SETUP, null, {
  serializer: {
    read: (v: string) => JSON.parse(v),
    // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
    write: (v: any | null) => JSON.stringify(v),
  },
});

// Form submission
async function handleSubmit() {
  loading.value = true;
  try {
    await useUserStore().updateUserProfile({
      displayName: profileSetup.value?.displayName ?? '',
      phoneNumber: profileSetup.value?.phoneNumber ?? '',
      phoneVerified: true,
      metadata: {
        location: location.value,
        sports: selectedSports.value,
      },
    });

    $q.notify({
      type: 'positive',
      message: 'Setup completed successfully!',
      position: 'top',
    });

    router.replace('/');
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: error.message || 'Failed to complete setup',
    });
  } finally {
    loading.value = false;
  }
}
</script>
