<template>
  <q-page class="column items-center justify-center q-pa-md">
    <div class="text-h5 text-weight-bold text-center q-mb-md">Complete Your Profile</div>
    <div class="text-body2 text-grey-6 text-center q-mb-lg">
      Don't worry, only you can see your personal data. No one else will be able to see it.
    </div>
    <div class="q-mb-md flex flex-center" style="position: relative; width: 88px; height: 88px">
      <q-avatar
        style="background-color: #f0f0f0"
        size="110px"
        class="cursor-pointer"
        @click="triggerFileInput"
      >
        <img v-if="photoURL" :src="photoURL" alt="avatar" style="object-fit: cover" />
        <q-icon v-else name="person" size="56px" color="grey-5" />
      </q-avatar>

      <div
        class="absolute-bottom-right bg-primary flex flex-center cursor-pointer"
        style="width: 40px; height: 40px; border-radius: 50%; transform: translate(50%, 50%)"
        @click="triggerFileInput"
      >
        <q-icon name="edit" size="18px" color="white" />
      </div>

      <input ref="fileInput" type="file" accept="image/*" class="hidden" @change="onFileChange" />
    </div>
    <q-card-section class="full-width q-my-xl">
      <q-form @submit="handleSubmit" class="q-gutter-y-md">
        <q-input
          id="name"
          v-model="displayName"
          placeholder="Name"
          outlined
          :rules="[(val) => !!val || 'Name is required']"
          error-message-class="q-mt-sm"
        />
        <PhoneInput
          v-model="phoneNumber"
          class="q-mb-md"
          @update:isValid="onPhoneValidChange"
          @update:countryCode="onCountryCodeChange"
        />
        <q-btn
          type="submit"
          color="primary"
          class="full-width q-mt-md"
          label="Complete"
          size="lg"
          :loading="loading"
          :disable="!displayName || !phoneNumber || !isPhoneValid || loading"
        />
      </q-form>
    </q-card-section>
  </q-page>
</template>
<script setup lang="ts">
import { useLocalStorage } from '@vueuse/core';
import { useQuasar } from 'quasar';
import PhoneInput from 'src/components/common/PhoneInput.vue';
import { LOCAL_STORAGE_KEYS } from 'src/constants';
import { useUserStore } from 'src/stores/userStore';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const $q = useQuasar();
const router = useRouter();
const userStore = useUserStore();

// Form data
const displayName = ref(userStore.user?.displayName || '');
const phoneNumber = ref(userStore.user?.phoneNumber || '');
const photoURL = ref(userStore.user?.photoURL || '');

const fileInput = ref<HTMLInputElement | null>(null);
const loading = ref(false);
const isPhoneValid = ref(false);
const countryCode = ref('VN');

const onPhoneValidChange = (valid: boolean) => {
  isPhoneValid.value = valid;
};

const onCountryCodeChange = (code: string) => {
  countryCode.value = code;
};

// File handling
const triggerFileInput = () => {
  fileInput.value?.click();
};

const onFileChange = (event: Event) => {
  const files = (event.target as HTMLInputElement).files;
  if (files && files[0]) {
    const reader = new FileReader();
    reader.onload = (e) => {
      photoURL.value = e.target?.result as string;
    };
    reader.readAsDataURL(files[0]);
  }
};

// Form submission
const handleSubmit = () => {
  try {
    loading.value = true;

    // Validate phone number
    if (!phoneNumber.value || !isPhoneValid.value) return;

    // Remove any non-digit characters from phone number and country code
    const cleanPhone = phoneNumber.value.replace(/\D/g, '');
    const cleanCountryCode = countryCode.value.replace(/[^\d]/g, '');
    const fullPhoneNumber = `+${cleanCountryCode}${cleanPhone}`;

    // Save profile data and verification ID
    useLocalStorage(LOCAL_STORAGE_KEYS.PROFILE_SETUP, {
      displayName: displayName.value,
      phoneNumber: fullPhoneNumber,
      photoURL: photoURL.value,
      phoneVerified: true,
    });

    $q.notify({
      type: 'positive',
      message: 'Verification code sent to your phone',
      position: 'top',
    });

    // Navigate to verification page
    router.push('/onboarding/location');
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: error.message || 'Failed to send verification code. Please try again.',
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
};
</script>
