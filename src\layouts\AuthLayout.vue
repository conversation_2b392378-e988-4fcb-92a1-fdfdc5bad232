<template>
  <q-layout view="lHh Lpr lFf">
    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
// Simple layout for authentication screens without width constraints
</script>

<style lang="scss" scoped>
.q-layout {
  // No max-width constraint for auth screens
  width: 100%;
  max-width: 690px !important;
  margin: 0 auto;
}
</style>
